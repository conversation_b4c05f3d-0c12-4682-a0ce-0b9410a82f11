// Package k8s provides Kubernetes cluster management functionality
// Implements configurable connections with graceful fallback behavior
package k8s

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"fyne.io/fyne/v2"

	"assistant-go/internal/config"
	"assistant-go/internal/types"
	"assistant-go/pkg/cybertheme"
)

// Manager handles Kubernetes operations using domain-specific managers
type Manager struct {
	config *config.Config
	logger *slog.Logger

	// Domain-specific managers
	clusterManager  *ClusterManager
	resourceManager *ResourceManager

	// Kubernetes clients for different contexts
	clients       map[string]*ClusterClient
	activeContext string
	mu            sync.RWMutex

	// Module state
	ctx     context.Context
	cancel  context.CancelFunc
	running bool

	// UI component
	ui *UI
}



// NewManager creates a new Kubernetes manager with domain-specific structure
func NewManager() *Manager {
	ctx, cancel := context.WithCancel(context.Background())

	clusterManager := NewClusterManager()
	resourceManager := NewResourceManager(clusterManager)

	return &Manager{
		clusterManager:  clusterManager,
		resourceManager: resourceManager,
		clients:         make(map[string]*ClusterClient),
		ctx:             ctx,
		cancel:          cancel,
		logger:          slog.Default().With("component", "k8s"),
	}
}

// Initialize implements the Module interface
func (m *Manager) Initialize(ctx context.Context, cfg *config.Config) error {
	m.config = cfg
	m.logger.Info("Initializing Kubernetes manager")

	// Initialize configured contexts
	for _, contextCfg := range cfg.K8s.Contexts {
		if contextCfg.Enabled {
			if err := m.AddContext(contextCfg); err != nil {
				m.logger.Error("Failed to add context", "name", contextCfg.Name, "error", err)
				// Continue with other contexts - graceful fallback
			}
		}
	}

	// Set default active context
	if m.config.K8s.DefaultContext != "" {
		m.activeContext = m.config.K8s.DefaultContext
	}

	m.logger.Info("Kubernetes manager initialized", "contexts", len(m.clients))
	return nil
}

// Start implements the Module interface
func (m *Manager) Start(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.running {
		return fmt.Errorf("kubernetes manager is already running")
	}

	m.logger.Info("Starting Kubernetes manager")

	// Start cluster monitoring
	go m.monitorClusters()

	m.running = true
	m.logger.Info("Kubernetes manager started successfully")
	return nil
}

// Stop implements the Module interface
func (m *Manager) Stop(ctx context.Context) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if !m.running {
		return nil
	}

	m.logger.Info("Stopping Kubernetes manager")

	// Cancel context to stop monitoring
	m.cancel()

	m.running = false
	m.logger.Info("Kubernetes manager stopped")
	return nil
}

// Name implements the Module interface
func (m *Manager) Name() string {
	return "k8s"
}

// Health implements the Module interface
func (m *Manager) Health() types.ModuleHealth {
	m.mu.RLock()
	defer m.mu.RUnlock()

	health := types.ModuleHealth{
		Status:    "healthy",
		Message:   fmt.Sprintf("%d contexts configured", len(m.clients)),
		LastCheck: time.Now(),
	}

	// Check if any contexts are unhealthy
	unhealthyCount := 0
	for _, client := range m.clients {
		status := client.GetStatus()
		if !status.Connected {
			unhealthyCount++
		}
	}

	if unhealthyCount > 0 {
		health.Status = "degraded"
		health.Message = fmt.Sprintf("%d of %d contexts unhealthy", unhealthyCount, len(m.clients))
	}

	if !m.running {
		health.Status = "unhealthy"
		health.Message = "Module not running"
	}

	if m.activeContext != "" {
		health.Message += fmt.Sprintf(" (active: %s)", m.activeContext)
	}

	return health
}

// AddContext adds a new Kubernetes context
func (m *Manager) AddContext(cfg config.K8sContext) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[cfg.Name]; exists {
		return fmt.Errorf("context %s already exists", cfg.Name)
	}

	client := &ClusterClient{
		Name:   cfg.Name,
		Config: cfg,
		Status: ClusterStatus{
			Connected: false,
			LastCheck: time.Now(),
		},
	}

	// Attempt to connect
	if err := client.Connect(); err != nil {
		m.logger.Error("Failed to connect to cluster", "name", cfg.Name, "error", err)
		// Store client even if it failed - user can retry
		client.Status.Error = err
	}

	m.clients[cfg.Name] = client
	m.logger.Info("Kubernetes context added", "name", cfg.Name, "connected", client.Status.Connected)

	return nil
}

// RemoveContext removes a Kubernetes context
func (m *Manager) RemoveContext(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; !exists {
		return fmt.Errorf("context %s does not exist", name)
	}

	delete(m.clients, name)

	// Update active context if it was removed
	if m.activeContext == name {
		m.activeContext = ""
		// Set to first available context
		for contextName := range m.clients {
			m.activeContext = contextName
			break
		}
	}

	m.logger.Info("Kubernetes context removed", "name", name)
	return nil
}

// GetContext returns a Kubernetes context by name
func (m *Manager) GetContext(name string) (*ClusterClient, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	client, exists := m.clients[name]
	if !exists {
		return nil, fmt.Errorf("context %s does not exist", name)
	}

	return client, nil
}

// ListContexts returns all Kubernetes contexts
func (m *Manager) ListContexts() map[string]*ClusterClient {
	m.mu.RLock()
	defer m.mu.RUnlock()

	// Return a copy to prevent external modification
	contexts := make(map[string]*ClusterClient)
	for name, client := range m.clients {
		contexts[name] = client
	}

	return contexts
}

// SetActiveContext sets the active Kubernetes context
func (m *Manager) SetActiveContext(name string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if _, exists := m.clients[name]; !exists {
		return fmt.Errorf("context %s does not exist", name)
	}

	m.activeContext = name
	m.logger.Info("Active context changed", "context", name)
	return nil
}

// GetActiveContext returns the active Kubernetes context
func (m *Manager) GetActiveContext() (*ClusterClient, error) {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if m.activeContext == "" {
		return nil, fmt.Errorf("no active context set")
	}

	client, exists := m.clients[m.activeContext]
	if !exists {
		return nil, fmt.Errorf("active context %s does not exist", m.activeContext)
	}

	return client, nil
}

// ListPods lists pods in the specified namespace
func (m *Manager) ListPods(namespace string) ([]ResourceInfo, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.ListPods(namespace)
}

// ListServices lists services in the specified namespace
func (m *Manager) ListServices(namespace string) ([]ResourceInfo, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.ListServices(namespace)
}

// ListNamespaces lists all namespaces
func (m *Manager) ListNamespaces() ([]string, error) {
	client, err := m.GetActiveContext()
	if err != nil {
		return nil, err
	}

	return client.GetNamespaces()
}

// monitorClusters monitors the health of all cluster connections
func (m *Manager) monitorClusters() {
	ticker := time.NewTicker(m.config.K8s.RefreshRate)
	defer ticker.Stop()

	for {
		select {
		case <-m.ctx.Done():
			return
		case <-ticker.C:
			m.checkClusterHealth()
		}
	}
}

// checkClusterHealth checks the health of all cluster connections
func (m *Manager) checkClusterHealth() {
	m.mu.RLock()
	clients := make([]*ClusterClient, 0, len(m.clients))
	for _, client := range m.clients {
		clients = append(clients, client)
	}
	m.mu.RUnlock()

	for _, client := range clients {
		go client.CheckHealth()
	}
}

// GetUI returns the UI component for the k8s module
func (m *Manager) GetUI() fyne.CanvasObject {
	if m.ui == nil {
		// Create cyberpunk theme
		theme := cybertheme.NewCyberTheme()

		// Initialize UI component
		m.ui = NewUI(m, m.config, theme)
	}

	return m.ui.Content()
}



